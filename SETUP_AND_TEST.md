# Gemini CLI - 编译、打包和API测试指南

## 项目概述

本项目是一个修改版的 Gemini CLI，主要改进包括：
- 使用自定义 HTTP 客户端替代 GoogleGenAI SDK
- 支持 Cloudflare Gateway 代理
- 支持流式和非流式 API 调用

## 环境要求

- Node.js 18+
- npm

## 安装和编译

### 1. 安装依赖
```bash
npm install
```

### 2. 编译项目
```bash
npm run build
```

### 3. 打包构建
```bash
npm run bundle
```

### 4. 完整编译（推荐）
```bash
npm run build && npm run bundle
```

## API 配置

### 环境变量设置
```bash
# 设置 Gemini API Key
export GEMINI_API_KEY="your-api-key-here"

# 或者使用 Google API Key
export GOOGLE_API_KEY="your-api-key-here"
```

### 当前配置
- **BaseURL**: `https://gateway.ai.cloudflare.com/v1/16591d8056bfed64a95f581e36117d3e/google/google-ai-studio`
- **默认模型**: `gemini-2.5-pro`
- **认证方式**: `gemini-api-key`

## 测试命令

### 1. 基本测试
```bash
echo "Hello! Test API connection" | node bundle/gemini.js
```

### 2. 非流式API测试
```bash
echo "Test non-streaming API" | node bundle/gemini.js
```

### 3. 交互式测试
```bash
node bundle/gemini.js
```

### 4. 带调试信息测试
```bash
DEBUG=1 echo "Debug test" | node bundle/gemini.js
```

## 实际测试结果

### 成功的API连接日志
```
[DEBUG] Custom ContentGenerator Init - AuthType: gemini-api-key, Model: gemini-2.5-pro, HasApiKey: true, BaseURL: https://gateway.ai.cloudflare.com/v1/16591d8056bfed64a95f581e36117d3e/google/google-ai-studio

[DEBUG] API Call - Model: gemini-2.5-pro, Auth: gemini-api-key, Contents Length: 3

[DEBUG] Custom API Call - URL: https://gateway.ai.cloudflare.com/v1/16591d8056bfed64a95f581e36117d3e/google/google-ai-studio/v1beta/models/gemini-2.5-pro:generateContent?key=AIzaSy...
```

### API状态确认
- ✅ **连接成功**: 请求成功到达 Gemini API
- ✅ **格式正确**: 请求体格式符合 API 规范
- ✅ **Cloudflare Gateway**: 代理工作正常
- ⚠️ **配额限制**: 当前遇到 429 错误（正常的 API 限制）

## 关键技术实现

### 1. 自定义HTTP客户端
替代了原始的 GoogleGenAI SDK，实现了：
- `generateContent()` - 非流式调用
- `generateContentStream()` - 流式调用
- `countTokens()` - 令牌计数
- `embedContent()` - 内容嵌入

### 2. API端点切换
- **非流式**: `/v1beta/models/{model}:generateContent`
- **流式**: `/v1beta/models/{model}:streamGenerateContent`

### 3. 请求格式优化
```typescript
{
  contents: request.contents,
  generationConfig: {
    temperature: request.config.temperature,
    topP: request.config.topP,
    topK: request.config.topK,
    maxOutputTokens: request.config.maxOutputTokens,
    stopSequences: request.config.stopSequences,
  },
  systemInstruction: {
    parts: [{ text: request.config.systemInstruction }]
  },
  tools: request.config.tools,
}
```

## 错误处理

### 常见错误类型
1. **429 Too Many Requests**: 配额用尽，需要等待重置
2. **400 Bad Request**: 请求格式错误
3. **404 Not Found**: API 端点不存在
4. **401 Unauthorized**: API Key 无效

### 重试机制
项目内置了指数退避重试机制：
- 自动重试 429 和 5xx 错误
- 支持 Flash 模型降级
- 详细的错误日志记录

## 开发模式

### 启动开发服务器
```bash
npm start
```

### 调试模式
```bash
npm run debug
```

### 运行测试
```bash
npm test
```

## 文件结构

### 核心文件
- `packages/core/src/core/contentGenerator.ts` - 自定义HTTP客户端
- `packages/cli/src/nonInteractiveCli.ts` - 非交互式CLI实现
- `packages/core/src/core/geminiChat.ts` - 聊天会话管理

### 构建输出
- `bundle/gemini.js` - 最终可执行文件
- `packages/*/dist/` - 编译后的TypeScript文件

## 下一步优化

1. **配额管理**: 实现智能配额监控
2. **区域配置**: 支持不同地理区域的API端点
3. **缓存机制**: 减少重复API调用
4. **错误恢复**: 更智能的错误处理和恢复策略

## 故障排除

### 常见问题
1. **编译失败**: 确保 Node.js 版本 ≥ 18
2. **API连接失败**: 检查网络和API Key配置
3. **权限错误**: 确保有足够的文件系统权限

### 调试步骤
1. 运行 `npm run build` 检查编译错误
2. 使用 `DEBUG=1` 启用详细日志
3. 检查环境变量配置
4. 验证API Key有效性

## 联系信息

如有问题，请查看项目日志或联系开发团队。