# 提示词预设相关文件搜索队列

## 搜索策略
根据用户要求，按照以下流程搜索与"提示词预设"相关的文件：

```
keywordList = (
    "prompt template system message role",
    "system prompt instruction template", 
    "预设 prompt preset",
    "conversation context template",
    "instruction role content message",
    "system message context memory",
    "AI assistant agent behavior",
    "guidelines rules mandates workflow"
)
```

## 搜索结果分析

### keyword1: "prompt template system message role"
**搜索结果文件：**
- `packages/core/src/core/prompts.ts` - 核心系统提示词生成器
- `packages/core/src/core/__snapshots__/prompts.test.ts.snap` - 提示词测试快照
- `packages/core/src/utils/nextSpeakerChecker.ts` - 对话流程控制提示词
- `packages/cli/src/ui/components/Help.tsx` - 帮助界面组件
- `packages/cli/src/ui/components/InputPrompt.tsx` - 输入提示组件
- `packages/core/src/telemetry/types.ts` - 用户提示词事件类型

**分析：** 找到了核心的提示词系统，主要集中在 `prompts.ts` 文件中

### keyword2: "system prompt instruction template"
**搜索结果文件：**
- `packages/core/src/core/__snapshots__/prompts.test.ts.snap` - 系统提示词快照测试
- `packages/core/src/core/prompts.ts` - 核心提示词模板
- `packages/cli/src/ui/contexts/SessionContext.tsx` - 会话上下文
- `packages/core/src/utils/nextSpeakerChecker.ts` - 下一个发言者检查器
- `packages/core/src/core/geminiChat.ts` - Gemini聊天实现
- `packages/core/src/core/client.ts` - 客户端实现

**分析：** 确认了系统指令模板的核心实现位置

### keyword3: "预设 prompt preset"
**搜索结果文件：**
- `packages/core/src/core/prompts.ts` - 核心提示词文件
- `packages/core/src/core/__snapshots__/prompts.test.ts.snap` - 提示词快照
- `packages/core/src/utils/nextSpeakerChecker.ts` - 对话流程检查器
- `packages/cli/src/ui/components/Help.tsx` - 帮助组件
- `packages/cli/src/ui/components/InputPrompt.tsx` - 输入提示组件
- `packages/core/src/telemetry/types.ts` - 遥测类型定义

**分析：** 没有找到明确的中文"预设"相关内容，但确认了英文prompt相关文件

### keyword4: "conversation context template"
**搜索结果文件：**
- `packages/cli/src/ui/contexts/SessionContext.tsx` - 会话状态管理
- `packages/cli/src/ui/hooks/slashCommandProcessor.ts` - 斜杠命令处理器
- `packages/core/src/core/geminiChat.ts` - 聊天会话管理
- `packages/core/src/code_assist/converter.ts` - 内容转换器
- `packages/cli/src/nonInteractiveCli.ts` - 非交互式CLI
- `packages/cli/src/ui/hooks/useGeminiStream.ts` - Gemini流处理钩子

**分析：** 找到了对话上下文和会话模板相关的实现文件

## 下一步搜索关键词
基于已有结果，继续搜索：
- "memory discovery context file"
- "GEMINI.md hierarchical memory"
- "system instruction configuration"
- "chat history compression"
- "model behavior configuration" 
