# Gemini CLI 项目提示词预设分析 - 第二部分

## 对话流程控制提示词分析

### 1. 下一个发言者检查器 (`packages/core/src/utils/nextSpeakerChecker.ts`)

| 要素 | 详细分析 |
|------|---------|
| **所在文件** | `packages/core/src/utils/nextSpeakerChecker.ts` |
| **角色定义** | 分析你前一个回应的内容和结构，确定下一个逻辑上应该发言的是"用户"还是"模型"（你） |
|              | 分析你前一个回应的内容和结构，确定下一个逻辑上应该发言的是"用户"还是"模型"（你） |
| **核心提示词** | Analyze *only* the content and structure of your immediately preceding response (your last turn in the conversation history). Based *strictly* on that response, determine who should logically speak next: the 'user' or the 'model' (you). |
|              | 仅分析你立即前一个回应的内容和结构（对话历史中你的最后一轮）。严格基于该回应，确定下一个逻辑上应该发言的是"用户"还是"模型"（你）。 |
| **决策规则** | 1. **Model Continues**: 如果最后回应明确说明你打算采取的下一个行动，或者回应明显不完整，则"模型"应该继续发言<br/>2. **Question to User**: 如果最后回应以直接向用户提出的问题结尾，则"用户"应该发言<br/>3. **Waiting for User**: 如果最后回应完成了一个想法、陈述或任务且不符合规则1或2，则暗示等待用户输入，"用户"应该发言 |
|              | 1. **模型继续**：如果最后回应明确说明你打算采取的下一个行动，或者回应明显不完整，则"模型"应该继续发言<br/>2. **向用户提问**：如果最后回应以直接向用户提出的问题结尾，则"用户"应该发言<br/>3. **等待用户**：如果最后回应完成了一个想法、陈述或任务且不符合规则1或2，则暗示等待用户输入，"用户"应该发言 |
| **输出格式** | JSON 结构：`{"reasoning": "简要解释", "next_speaker": "user|model"}` |
|              | JSON 结构：`{"reasoning": "简要解释", "next_speaker": "user|model"}` |
| **特殊处理** | - 函数响应消息：如果最后消息是函数响应，模型应该继续<br/>- 空内容消息：如果模型消息没有内容，模型应该继续 |
|              | - 函数响应消息：如果最后消息是函数响应，模型应该继续<br/>- 空内容消息：如果模型消息没有内容，模型应该继续 |

### 2. 对话流程控制逻辑

系统实现了智能的对话流程控制：

```typescript
const CHECK_PROMPT = `Analyze *only* the content and structure of your immediately preceding response...`;
```

这个提示词确保了：
1. **精确分析**: 只分析前一个回应，不受整个对话历史影响
2. **结构化决策**: 通过明确的规则进行决策
3. **JSON 输出**: 确保结果可以被程序化处理

## 内存管理系统提示词分析

### 1. 内存工具 (`packages/core/src/tools/memoryTool.ts`)

| 要素 | 详细分析 |
|------|---------|
| **所在文件** | `packages/core/src/tools/memoryTool.ts` |
| **工具名称** | `save_memory` |
| **角色定义** | Saves a specific piece of information or fact to your long-term memory. Use this when the user explicitly asks you to remember something, or when they state a clear, concise fact that seems important to retain for future interactions. |
|              | 将特定信息或事实保存到你的长期记忆中。当用户明确要求你记住某事，或当他们陈述一个对未来交互很重要的清晰、简洁的事实时使用此工具。 |
| **使用场景** | - 用户明确要求记住某事（例如："记住我喜欢菠萝披萨"）<br/>- 用户陈述关于自己、偏好或环境的清晰事实，对提供个性化帮助很重要 |
|              | - 用户明确要求记住某事（例如："记住我喜欢菠萝披萨"）<br/>- 用户陈述关于自己、偏好或环境的清晰事实，对提供个性化帮助很重要 |
| **禁用场景** | - 记住仅与当前会话相关的对话上下文<br/>- 保存长篇、复杂或冗长的文本<br/>- 不确定信息是否值得长期记忆时 |
|              | - 记住仅与当前会话相关的对话上下文<br/>- 保存长篇、复杂或冗长的文本<br/>- 不确定信息是否值得长期记忆时 |
| **参数规格** | `fact` (string, required): 要记住的特定事实或信息。应该是一个清晰、自包含的陈述 |
|              | `fact` (string, required): 要记住的特定事实或信息。应该是一个清晰、自包含的陈述 |

### 2. 层级化内存发现系统 (`packages/core/src/utils/memoryDiscovery.ts`)

| 要素 | 详细分析 |
|------|---------|
| **所在文件** | `packages/core/src/utils/memoryDiscovery.ts` |
| **系统功能** | 实现层级化的 GEMINI.md 文件发现和加载机制 |
|              | 实现层级化的 GEMINI.md 文件发现和加载机制 |
| **搜索策略** | 1. **全局内存**: 从用户主目录的 `.gemini/GEMINI.md` 开始<br/>2. **向上搜索**: 从当前工作目录向上搜索到项目根目录<br/>3. **向下搜索**: 在当前工作目录及其子目录中广度优先搜索 |
|              | 1. **全局内存**：从用户主目录的 `.gemini/GEMINI.md` 开始<br/>2. **向上搜索**：从当前工作目录向上搜索到项目根目录<br/>3. **向下搜索**：在当前工作目录及其子目录中广度优先搜索 |
| **文件优先级** | 全局配置 → 项目级配置 → 当前目录配置 → 子目录配置 |
|              | 全局配置 → 项目级配置 → 当前目录配置 → 子目录配置 |
| **配置机制** | - 支持多个 GEMINI.md 文件名<br/>- 可配置最大扫描目录数量（默认200）<br/>- 调试模式支持详细日志 |
|              | - 支持多个 GEMINI.md 文件名<br/>- 可配置最大扫描目录数量（默认200）<br/>- 调试模式支持详细日志 |

### 3. 内存系统架构特点

1. **层级化设计**: 支持全局、项目、目录级别的配置文件
2. **自动发现**: 智能搜索和加载相关配置文件
3. **可扩展性**: 支持多个文件名和自定义搜索策略
4. **性能优化**: 限制搜索范围，避免过度扫描

## GEMINI.md 项目配置文件分析

### 项目特定上下文 (`GEMINI.md`)

| 要素 | 详细分析 |
|------|---------|
| **所在文件** | `GEMINI.md` |
| **构建指令** | Before submitting any changes, it is crucial to validate them by running the full preflight check: `npm run preflight` |
|              | 在提交任何更改之前，通过运行完整的预检查来验证它们至关重要：`npm run preflight` |
| **测试框架** | 项目使用 **Vitest** 作为主要测试框架。测试文件与源文件共存，使用 `*.test.ts` 和 `*.test.tsx` 命名约定 |
|              | 项目使用 **Vitest** 作为主要测试框架。测试文件与源文件共存，使用 `*.test.ts` 和 `*.test.tsx` 命名约定 |
| **模拟指导** | - ES 模块模拟：`vi.mock('module-name', async (importOriginal) => { ... })`<br/>- 模拟顺序：对于关键依赖项，将 `vi.mock` 放在测试文件顶部<br/>- 提升：使用 `vi.hoisted(() => vi.fn())` 定义需要提前使用的模拟函数 |
|              | - ES 模块模拟：`vi.mock('module-name', async (importOriginal) => { ... })`<br/>- 模拟顺序：对于关键依赖项，将 `vi.mock` 放在测试文件顶部<br/>- 提升：使用 `vi.hoisted(() => vi.fn())` 定义需要提前使用的模拟函数 |
| **编程范式** | 优先使用带有 TypeScript 接口或类型声明的普通 JavaScript 对象，而不是 JavaScript 类语法 |
|              | 优先使用带有 TypeScript 接口或类型声明的普通 JavaScript 对象，而不是 JavaScript 类语法 |
| **React 指导** | 作为 React 助手，帮助用户编写更高效和可优化的 React 代码，专门识别能够让 React Compiler 自动应用优化的模式 |
|              | 作为 React 助手，帮助用户编写更高效和可优化的 React 代码，专门识别能够让 React Compiler 自动应用优化的模式 |

## 系统集成特点

1. **多层次配置**: 从全局到项目到目录级别的配置层次
2. **智能发现**: 自动发现和加载相关配置文件
3. **上下文感知**: 根据项目特点提供专门的指导
4. **动态适应**: 根据环境和配置动态调整行为

这种设计使得 Gemini CLI 能够为不同的项目和用户提供高度个性化和上下文相关的 AI 助手体验。 
