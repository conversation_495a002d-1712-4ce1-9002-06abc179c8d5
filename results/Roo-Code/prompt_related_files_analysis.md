# 提示词预设相关文件分析

## 搜索词: `prompt`

根据初步搜索，以下文件与“提示词预设”高度相关：

-   `packages/core/src/core/prompts.ts`: 该文件包含核心功能 `getCoreSystemPrompt`，用于获取系统提示词。它揭示了一个通过环境变量 `GEMINI_SYSTEM_MD` 从外部文件（默认为 `.gemini/system.md`）加载系统提示词的机制。这可以被视为一种提示词预设的实现。

-   `packages/core/src/core/prompts.test.ts`: `prompts.ts` 的测试文件。

-   `packages/core/src/core/__snapshots__/prompts.test.ts.snap`: 测试快照文件，包含了基础系统提示词的完整内容。

### 分析

`prompts.ts` 文件中的 `getCoreSystemPrompt` 函数是关键。它允许用户通过设置 `GEMINI_SYSTEM_MD` 环境变量来覆盖默认的系统提示词，从而实现自定义的“提示词预设”。

### 提取的新关键词

-   `GEMINI_SYSTEM_MD`
-   `system.md`
-   `getCoreSystemPrompt`
-   `system_prompt`
-   `basePrompt`
-   `preset`
-   `template`

---

## 搜索词: `GEMINI_SYSTEM_MD`

搜索此环境变量进一步确认了 `packages/core/src/core/prompts.ts` 的核心地位。

-   `packages/core/src/core/prompts.ts`: 明确了 `GEMINI_SYSTEM_MD` 环境变量的用途：
    1.  如果设置为 `1` 或 `true`，则从默认路径 `.gemini/system.md` 加载系统提示。
    2.  如果设置为其他非 `0` 或 `false` 的值，则将该值视为自定义系统提示文件的路径。
    3.  该文件还引用了另一个相关环境变量 `GEMINI_WRITE_SYSTEM_MD`，用于将当前的系统提示写出到文件。

-   `packages/cli/src/ui/App.tsx`: 在 UI 中检查此环境变量是否存在，如果存在，则显示一个特殊图标 `|⌐■_■|`，以提示用户当前正在使用自定义的系统提示。

### 提取的新关键词

-   `GEMINI_WRITE_SYSTEM_MD`
-   `GEMINI_CONFIG_DIR`

---

## 搜索词: `system.md`

此搜索确认了 `system.md` 是在 `packages/core/src/core/prompts.ts` 中使用的默认自定义提示文件名。没有发现新的相关文件。

### 提取的新关键词

（无）

---

## 搜索词: `getCoreSystemPrompt`

此搜索确认了 `getCoreSystemPrompt` 函数在 `packages/core/src/core/client.ts` 中被广泛用于获取系统提示，是实现“提示词预设”功能的核心。没有发现新的相关文件。

### 提取的新关键词

（无）

---

## 搜索词: `system_prompt`

此搜索没有返回任何结果。

### 提取的新关键词

（无）

---

## 搜索词: `basePrompt`

此搜索确认 `basePrompt` 是 `packages/core/src/core/prompts.ts` 文件中的一个关键变量，用于存储基础系统提示词的内容。它要么从 `system.md` 文件加载，要么使用内置的默认提示。没有发现新的相关文件。

### 提取的新关键词

（无）

---

## 搜索词: `preset`

此搜索只返回了一个位于 `node_modules` 中的无关结果 (`@babel/preset-env`)。没有发现与提示词预设相关的文件。

### 提取的新关键词

（无）

---

## 搜索词: `template`

此搜索返回的结果主要与 UI 主题中的语法高亮和 GitHub 问题模板相关，均与“提示词预设”功能无关。

### 提取的新关键词

（无）

---

## 搜索词: `GEMINI_WRITE_SYSTEM_MD`

此搜索确认 `GEMINI_WRITE_SYSTEM_MD` 是 `packages/core/src/core/prompts.ts` 中使用的一个环境变量，用于将当前的系统提示（`basePrompt`）写出到文件，方便用户查看或修改。没有发现新的相关文件。

### 提取的新关键词

（无）

---

## 搜索词: `GEMINI_CONFIG_DIR`

此搜索确认 `GEMINI_CONFIG_DIR` 是在 `packages/core/src/tools/memoryTool.ts` 中定义的一个常量，值为 `.gemini`。它在 `packages/core/src/core/prompts.ts` 中被用来确定 `system.md` 文件的默认位置。没有发现新的相关文件。

### 提取的新关键词

（无）

---
