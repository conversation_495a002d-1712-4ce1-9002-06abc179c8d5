# Prompt-Related Files Analysis

## Initial Analysis

The core mechanism for "prompt presets" is located in `packages/core/src/core/prompts.ts`.

This file contains the function `getCoreSystemPrompt`, which is responsible for constructing the main system prompt for the CLI agent.

### Key Findings:

- **Customizable System Prompt:** The application supports loading a custom system prompt from an external Markdown file.
- **Environment Variable:** This feature is controlled by the `GEMINI_SYSTEM_MD` environment variable.
- **Default Path:** If the variable is set to `1` or `true`, the system attempts to load the prompt from `.gemini/system.md`.
- **Custom Path:** If the variable is set to a file path, the system will load the prompt from that specific path.

This indicates that the primary way to implement a "prompt preset" is by creating a `.gemini/system.md` file or pointing the environment variable to a custom prompt file.
