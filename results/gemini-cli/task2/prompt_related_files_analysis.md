# Prompt-Related Files Analysis

## Keyword: `prompts`

### File: `packages/core/src/core/prompts.ts`

This file is central to defining the system prompt for the Gemini CLI.

**Key Findings:**

*   **`getCoreSystemPrompt` function:** This function constructs the main system prompt.
*   **Default System Prompt:** A detailed, hardcoded prompt within the file defines the agent's core mandates, workflows, and operational guidelines.
*   **Environment Variable Override:** The system prompt can be overridden by setting the `GEMINI_SYSTEM_MD` environment variable to the path of a custom Markdown file. This provides a mechanism for users to define their own "preset" system prompts.
*   **User Memory Integration:** The prompt can be appended with user-specific information (`userMemory`), suggesting a personalization feature.

**Conclusion:** This file reveals a primary mechanism for prompt customization through external files, which directly relates to the concept of "prompt presets".

## Keyword: `context`

### Files: 
- `packages/cli/src/config/config.ts`
- `packages/cli/src/config/settings.ts`
- `packages/cli/src/config/extension.ts`
- `packages/core/src/utils/memoryDiscovery.ts`
- `packages/core/src/tools/memoryTool.ts`

**Key Findings:**

*   **`contextFileName`:** A key setting in `settings.json` that allows users to specify one or more files to be used as context. The default is `GEMINI.md`. This is a powerful feature for creating reusable prompt presets.
*   **Hierarchical Loading:** The `memoryDiscovery.ts` file implements a system for loading context files from various locations, including the user's home directory, the project root, and subdirectories, creating a layered context.
*   **Extension-provided Context:** Extensions can also contribute their own context files, making the system modular and extensible.
*   **`fullContext` Flag:** A boolean option to include the entire content of all project files in the context, providing maximum information to the model when needed.

**Conclusion:** The concept of "context" is deeply integrated and highly configurable. It's the primary way to provide large amounts of background information and instructions to the model, serving the function of "prompt presets" in a flexible and powerful manner. The system is designed around `GEMINI.md` as the default context file, but it can be customized extensively.

## Keyword: `history`

### Files:
- `packages/cli/src/ui/hooks/useHistoryManager.ts`
- `packages/cli/src/ui/hooks/useInputHistory.ts`
- `packages/cli/src/ui/hooks/useShellHistory.ts`
- `packages/core/src/core/geminiChat.ts`
- `packages/cli/src/ui/hooks/slashCommandProcessor.ts`
- `packages/core/src/services/gitService.ts`

**Key Findings:**

*   **Multiple History Types:** The application maintains separate histories for the UI display, user input navigation, and shell command execution.
*   **Core Conversation History:** `packages/core/src/core/geminiChat.ts` manages the authoritative history of the conversation with the AI model.
*   **Persistence and Checkpoints:** The `/chat save [tag]` and `/chat resume [tag]` slash commands allow users to save and load conversation histories, effectively creating checkpoints. This is handled in `slashCommandProcessor.ts`.
*   **Git-based Versioning:** `gitService.ts` reveals a sophisticated mechanism for versioning conversation history using Git in the `~/.gemini/history/` directory.

**Conclusion:** While not a direct "prompt preset" feature, the ability to save and resume chat sessions provides a similar capability. Users can start a session with a specific setup, save it, and then resume it later to reuse the initial context and conversation flow. This serves as a powerful workflow for managing different tasks or projects.
