# Gemini CLI 提示词预设相关文件整理分析

## 核心提示词文件

### 1. 主要系统提示词生成器
**文件**: `packages/core/src/core/prompts.ts`
**功能**: 核心系统提示词生成和配置
**关键函数**: `getCoreSystemPrompt(userMemory?: string): string`

### 2. 对话流程控制
**文件**: `packages/core/src/utils/nextSpeakerChecker.ts`
**功能**: 分析对话流程，决定下一个发言者
**关键提示词**: `CHECK_PROMPT` - 分析前一个回应内容和结构

### 3. 内存管理工具
**文件**: `packages/core/src/tools/memoryTool.ts`
**功能**: 长期记忆保存和管理
**工具名称**: `save_memory`

## 配置相关文件

### 1. 项目特定配置
**文件**: `GEMINI.md`
**功能**: 项目级别的配置和指导原则

### 2. 核心配置系统
**文件**: `packages/core/src/config/config.ts`
**功能**: 核心配置管理

### 3. CLI配置
**文件**: `packages/cli/src/config/config.ts`
**功能**: CLI特定配置

### 4. 内存发现系统
**文件**: `packages/core/src/utils/memoryDiscovery.ts`
**功能**: 层级化内存文件发现和加载

## 用户界面相关文件

### 1. 命令处理器
**文件**: `packages/cli/src/ui/hooks/slashCommandProcessor.ts`
**功能**: 处理斜杠命令

### 2. 流处理钩子
**文件**: `packages/cli/src/ui/hooks/useGeminiStream.ts`
**功能**: 处理Gemini API流式响应

### 3. 输入组件
**文件**: `packages/cli/src/ui/components/InputPrompt.tsx`
**功能**: 用户输入界面组件

### 4. 隐私通知组件
**文件**: `packages/cli/src/ui/privacy/CloudFreePrivacyNotice.tsx`
**功能**: 云服务隐私通知界面

## 核心逻辑文件

### 1. 聊天核心逻辑
**文件**: `packages/core/src/core/geminiChat.ts`
**功能**: Gemini聊天功能的核心实现

### 2. 客户端管理
**文件**: `packages/core/src/core/client.ts`
**功能**: 客户端连接和管理

### 3. 轮次管理
**文件**: `packages/core/src/core/turn.ts`
**功能**: 对话轮次的管理和控制

## 测试相关文件

### 1. 提示词测试快照
**文件**: `packages/core/src/core/__snapshots__/prompts.test.ts.snap`
**功能**: 提示词生成的测试快照

### 2. 其他测试文件
- `packages/core/src/core/prompts.test.ts`
- `packages/core/src/utils/nextSpeakerChecker.test.ts`
- `packages/cli/src/ui/hooks/slashCommandProcessor.test.ts`

## 文档相关文件

### 1. 配置文档
**文件**: `docs/cli/configuration.md`
**功能**: CLI配置说明文档

### 2. 命令文档
**文件**: `docs/cli/commands.md`
**功能**: CLI命令使用文档

### 3. 架构文档
**文件**: `docs/architecture.md`
**功能**: 项目架构说明

## 分析文档

### 1. 第一部分分析
**文件**: `results/cursor/gemini_cli_prompt_analysis_part1.md`
**功能**: 核心提示词系统分析

### 2. 第二部分分析
**文件**: `results/cursor/gemini_cli_prompt_analysis_part2.md`
**功能**: 对话流程和内存系统分析

## 关键环境变量和配置

### 系统提示词配置
- `GEMINI_SYSTEM_MD`: 覆盖默认系统提示词文件路径
- `GEMINI_WRITE_SYSTEM_MD`: 写入系统提示词到文件
- `SANDBOX`: 沙盒模式配置

### 内存系统配置
- `GEMINI_CONFIG_DIR`: Gemini配置目录（默认：.gemini）

## 文件依赖关系

```
prompts.ts (核心)
├── memoryTool.ts (内存工具)
├── nextSpeakerChecker.ts (流程控制)
├── memoryDiscovery.ts (内存发现)
├── config.ts (配置系统)
└── GEMINI.md (项目配置)

用户界面层
├── slashCommandProcessor.ts
├── useGeminiStream.ts
├── InputPrompt.tsx
└── 各种隐私和认证组件

核心逻辑层
├── geminiChat.ts
├── client.ts
└── turn.ts
```

## 提示词系统工作流程

1. **初始化**: `getCoreSystemPrompt()` 生成基础系统提示词
2. **内存集成**: 通过 `memoryDiscovery` 加载用户内存
3. **配置覆盖**: 通过环境变量或GEMINI.md进行配置
4. **对话管理**: `nextSpeakerChecker` 控制对话流程
5. **命令处理**: `slashCommandProcessor` 处理用户命令
6. **流式处理**: `useGeminiStream` 处理AI响应流

这个系统形成了一个完整的、可配置的、层级化的提示词预设管理架构。