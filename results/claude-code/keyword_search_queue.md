# Gemini CLI 提示词预设相关文件搜索队列

## 搜索策略流程图

```
keywordList = (
    "提示词预设",      # 初始中文关键词
    "prompt",          # 核心英文词汇
    "system",          # 系统提示词
    "template",        # 模板相关
    "memory",          # 记忆系统
    "config",          # 配置文件
    "getCoreSystemPrompt",  # 核心函数名
    "nextSpeakerChecker",   # 流程控制
    "memoryTool",          # 内存工具
    "GEMINI.md",           # 项目配置
    "slashCommand",        # 斜杠命令
    "assistant",           # 助手角色
    "instruction",         # 指令相关
    "conversation",        # 对话管理
    "role"                 # 角色定义
)

keyword1: "提示词预设|prompt|system"
   |
   ↓
packages/core/src/core/prompts.ts:getCoreSystemPrompt() [CORE]
packages/core/src/utils/nextSpeakerChecker.ts:CHECK_PROMPT [FLOW]
packages/core/src/tools/memoryTool.ts:save_memory [MEMORY]
GEMINI.md:项目特定配置 [CONFIG]
results/cursor/gemini_cli_prompt_analysis_part1.md [ANALYSIS]
results/cursor/gemini_cli_prompt_analysis_part2.md [ANALYSIS]
   |
   ↓
read all content
   |
   ↓
generate keyword2: "getCoreSystemPrompt|basePrompt|systemMdEnabled"
                |
                ↓
keyword2: "getCoreSystemPrompt|basePrompt|systemMdEnabled"
   |
   ↓
packages/core/src/core/prompts.ts:21-273 [SYSTEM_PROMPT_GENERATOR]
packages/core/src/core/client.ts [CLIENT_INTEGRATION]
packages/core/src/config/config.ts [CONFIG_SYSTEM]
   |
   ↓
generate keyword3: "nextSpeakerChecker|CHECK_PROMPT|next_speaker"
                |
                ↓
keyword3: "nextSpeakerChecker|CHECK_PROMPT|next_speaker"
   |
   ↓
packages/core/src/utils/nextSpeakerChecker.ts:CHECK_PROMPT [FLOW_CONTROL]
packages/core/src/core/turn.ts [TURN_MANAGEMENT]
packages/core/src/core/geminiChat.ts [CHAT_LOGIC]
   |
   ↓
generate keyword4: "memoryTool|save_memory|GEMINI_CONFIG_DIR"
                |
                ↓
keyword4: "memoryTool|save_memory|GEMINI_CONFIG_DIR"
   |
   ↓
packages/core/src/tools/memoryTool.ts:MEMORY_TOOL [MEMORY_SYSTEM]
packages/core/src/utils/memoryDiscovery.ts:findMemoryFiles [DISCOVERY]
   |
   ↓
generate keyword5: "slashCommand|commandProcessor|useGeminiStream"
                |
                ↓
keyword5: "slashCommand|commandProcessor|useGeminiStream"
   |
   ↓
packages/cli/src/ui/hooks/slashCommandProcessor.ts [COMMAND_PROCESSING]
packages/cli/src/ui/hooks/useGeminiStream.ts [STREAM_HANDLING]
packages/cli/src/ui/components/InputPrompt.tsx [UI_INPUT]
   |
   ↓
generate keyword6: "privacy|CloudFreePrivacyNotice|authentication"
                |
                ↓
keyword6: "privacy|CloudFreePrivacyNotice|authentication"
   |
   ↓
packages/cli/src/ui/privacy/CloudFreePrivacyNotice.tsx [PRIVACY_UI]
packages/cli/src/ui/privacy/GeminiPrivacyNotice.tsx [PRIVACY_GEMINI]
packages/cli/src/ui/hooks/useAuthCommand.ts [AUTH_COMMAND]
   |
   ↓
generate keyword7: "theme|colorizer|UI|component"
```

## 关键词搜索结果分析

### 第一轮搜索: "提示词预设|prompt|system"
- **核心发现**: 92个相关文件
- **关键文件**:
  - `packages/core/src/core/prompts.ts` - 系统提示词生成器
  - `packages/core/src/utils/nextSpeakerChecker.ts` - 对话流程控制
  - `packages/core/src/tools/memoryTool.ts` - 内存管理工具
  - `GEMINI.md` - 项目配置文件

### 第二轮搜索: "instruction|config|设置"
- **核心发现**: 57个相关文件
- **关键文件**:
  - `packages/core/src/config/config.ts` - 配置系统
  - `packages/cli/src/config/config.ts` - CLI配置
  - `docs/cli/configuration.md` - 配置文档

### 第三轮搜索: "gemini|claude|ai|model"
- **核心发现**: 296个相关文件（过多，需要细化）
- **核心模式**: AI模型集成贯穿整个项目

### 第四轮搜索: "chat|conversation|对话"
- **核心发现**: 35个相关文件
- **关键文件**:
  - `packages/core/src/core/geminiChat.ts` - 聊天核心逻辑
  - `packages/core/src/core/client.ts` - 客户端管理

### 第五轮搜索: "role|角色|assistant|用户"
- **核心发现**: 32个相关文件
- **关键文件**:
  - 角色定义散布在多个核心文件中

## 基于内容分析生成的新关键词队列

### 队列1: 核心系统提示词相关
- `getCoreSystemPrompt`
- `basePrompt`
- `systemMdEnabled`
- `GEMINI_SYSTEM_MD`
- `userMemory`

### 队列2: 对话流程控制相关
- `nextSpeakerChecker`
- `CHECK_PROMPT`
- `next_speaker`
- `reasoning`
- `turn.ts`

### 队列3: 内存系统相关
- `memoryTool`
- `save_memory`
- `GEMINI_CONFIG_DIR`
- `memoryDiscovery`
- `findMemoryFiles`

### 队列4: 命令处理相关
- `slashCommandProcessor`
- `useGeminiStream`
- `commandProcessor`
- `InputPrompt`

### 队列5: UI和主题相关
- `privacy`
- `CloudFreePrivacyNotice`
- `theme`
- `CodeColorizer`

### 队列6: 配置和认证相关
- `authentication`
- `useAuthCommand`
- `sandboxConfig`
- `config.ts`

## 提示词预设文件架构总结

```
提示词预设系统架构
├── 核心系统 (Core System)
│   ├── prompts.ts - 主要提示词生成器
│   ├── client.ts - 客户端集成
│   └── geminiChat.ts - 聊天逻辑
├── 流程控制 (Flow Control)
│   ├── nextSpeakerChecker.ts - 对话流程
│   └── turn.ts - 轮次管理
├── 内存系统 (Memory System)
│   ├── memoryTool.ts - 内存工具
│   └── memoryDiscovery.ts - 内存发现
├── 用户界面 (User Interface)
│   ├── slashCommandProcessor.ts - 命令处理
│   ├── useGeminiStream.ts - 流处理
│   └── InputPrompt.tsx - 输入组件
├── 配置系统 (Configuration)
│   ├── config.ts (core) - 核心配置
│   ├── config.ts (cli) - CLI配置
│   └── GEMINI.md - 项目配置
└── 分析文档 (Analysis Documents)
    ├── gemini_cli_prompt_analysis_part1.md
    └── gemini_cli_prompt_analysis_part2.md
```

## 下一步搜索计划

1. **深入核心函数**: 搜索 `getCoreSystemPrompt` 的所有调用点
2. **命令系统**: 搜索 `slashCommandProcessor` 的相关实现
3. **主题系统**: 搜索 `theme` 和 `colorizer` 相关文件
4. **测试文件**: 搜索 `*.test.ts` 中的提示词相关测试
5. **文档系统**: 搜索 `docs/` 目录下的相关文档

## 搜索优先级

1. **高优先级**: 核心提示词生成和流程控制
2. **中优先级**: 内存系统和配置管理
3. **低优先级**: UI组件和主题系统